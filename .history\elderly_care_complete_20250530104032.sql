
-- 居家养老健康管理系统数据库创建脚本
-- 创建时间: 2025年
-- 说明: 老人注册后自动分配区域A或B，区域A由刘强负责，区域B由王丽负责

-- 创建数据库
CREATE DATABASE IF NOT EXISTS elderly_care CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE elderly_care;

-- 老年人用户表
CREATE TABLE IF NOT EXISTS elderlyuser (
    user_id VARCHAR(10) PRIMARY KEY,
    password VARCHAR(50) NOT NULL,
    name VARCHAR(50) NOT NULL,
    age INT,
    phone VARCHAR(20),
    address VARCHAR(100),
    emergency_contact_name VARCHAR(50),
    emergency_contact_phone VARCHAR(20),
    health_record_id VARCHAR(10),
    smartwatch_id VARCHAR(10),
    bound_family_ids VARCHAR(100),
    region VARCHAR(10) COMMENT '老人所属区域（A或B）'
);

-- 家属用户表
CREATE TABLE IF NOT EXISTS familyuser (
    family_id VARCHAR(10) PRIMARY KEY,
    password VARCHAR(50) NOT NULL,
    name VARCHAR(50) NOT NULL,
    relationship VARCHAR(20),
    phone VARCHAR(20),
    bound_elderly_id VARCHAR(10),
    FOREIGN KEY (bound_elderly_id) REFERENCES elderlyuser(user_id)
);

-- 社区工作人员表
CREATE TABLE IF NOT EXISTS communityworker (
    worker_id VARCHAR(10) PRIMARY KEY,
    password VARCHAR(50) NOT NULL,
    name VARCHAR(50) NOT NULL,
    region VARCHAR(10) COMMENT '负责区域（A或B）',
    phone VARCHAR(20)
);

-- 智能手环表
CREATE TABLE IF NOT EXISTS smartwatch (
    watch_id VARCHAR(10) PRIMARY KEY,
    bound_user_id VARCHAR(10),
    last_sync_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    gps_location VARCHAR(50) COMMENT '格式: 纬度,经度',
    heart_rate INT,
    sleep_score INT,
    step_count INT,
    battery INT,
    FOREIGN KEY (bound_user_id) REFERENCES elderlyuser(user_id)
);

-- 健康档案表
CREATE TABLE IF NOT EXISTS healthrecord (
    record_id VARCHAR(10) PRIMARY KEY,
    blood_pressure VARCHAR(20),
    blood_sugar VARCHAR(20),
    medication_record VARCHAR(1000),
    medical_history VARCHAR(1000),
    update_time DATE,
    health_threshold VARCHAR(1000)
);

-- 紧急呼叫记录表
CREATE TABLE IF NOT EXISTS emergencycall (
    call_id VARCHAR(20) PRIMARY KEY,
    elderly_id VARCHAR(10),
    call_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    gps_location VARCHAR(50) COMMENT '格式: 纬度,经度',
    status VARCHAR(20) DEFAULT '待处理' COMMENT '待处理/已响应/已解决',
    related_person_id VARCHAR(10) COMMENT '可能是家属ID或社区工作人员ID',
    FOREIGN KEY (elderly_id) REFERENCES elderlyuser(user_id)
);

-- 健康科普文章表
CREATE TABLE IF NOT EXISTS healtharticle (
    article_id VARCHAR(10) PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(20) NOT NULL COMMENT '文章分类：养生、急救、饮食、运动、疾病管理、用药、心理、保健等',
    publish_time DATE NOT NULL,
    favorited_by_users VARCHAR(200) COMMENT '收藏用户ID列表，逗号分隔',
    image_url VARCHAR(200) COMMENT '文章配图URL',
    read_count INT DEFAULT 0 COMMENT '阅读次数',
    difficulty_level INT DEFAULT 1 COMMENT '难度等级：1-简单，2-适中，3-复杂'
);

-- 插入社区工作人员数据（刘强负责区域A，王丽负责区域B）
INSERT INTO communityworker (worker_id, password, name, region, phone) VALUES
('W_A01', '123456', '刘强', 'A', '13900000001'),
('W_B02', '123456', '王丽', 'B', '13900000002')
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- 插入测试老年人用户数据（自动分配区域）
INSERT INTO elderlyuser (user_id, password, name, age, phone, address, emergency_contact_name, emergency_contact_phone, health_record_id, smartwatch_id, bound_family_ids, region) VALUES
('E01', '123456', '张建国', 78, '13800000001', '浙江省杭州市余杭区龙湖天街1号', '张志强', '13800000011', 'HR01', 'SW01', 'F01,F02', 'A'),
('E02', '123456', '李淑兰', 75, '13800000002', '浙江省杭州市西湖区西溪路518号', '李朝阳', '13800000021', 'HR02', 'SW02', 'F03,F04', 'B'),
('E03', '123456', '王福海', 80, '13800000003', '浙江省杭州市拱墅区湖墅南路88号', '王宇轩', '13800000031', 'HR03', 'SW03', 'F05,F06', 'A'),
('E04', '123456', '刘秀英', 75, '13800000004', '浙江省杭州市余杭区龙湖天街1号', '张志强', '13800000011', 'HR04', 'SW04', 'F07,F08', 'A')
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- 插入家属用户数据
INSERT INTO familyuser (family_id, password, name, relationship, phone, bound_elderly_id) VALUES
('F01', '123456', '张志强', '儿子', '13800000011', 'E01'),
('F02', '123456', '张慧敏', '女儿', '13800000012', 'E01'),
('F03', '123456', '李朝阳', '儿子', '13800000021', 'E02'),
('F04', '123456', '李雅婷', '女儿', '13800000022', 'E02'),
('F05', '123456', '王宇轩', '儿子', '13800000031', 'E03'),
('F06', '123456', '王诗涵', '女儿', '13800000032', 'E03'),
('F07', '123456', '陈俊豪', '儿子', '13800000041', 'E04'),
('F08', '123456', '陈梦琪', '女儿', '13800000042', 'E04')
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- 插入健康档案数据
INSERT INTO healthrecord (record_id, blood_pressure, blood_sugar, medication_record, medical_history, update_time, health_threshold) VALUES
('HR01', '120/80', '5.6', '阿司匹林:100mg:每日一次', '高血压', '2025-05-01', '血压<140/90，心率<100'),
('HR02', '130/85', '6.2', '格列美脲:2mg:每日一次', '糖尿病', '2025-05-01', '血压<140/90，心率<100'),
('HR03', '140/90', '5.9', '贝那普利:10mg:每日一次', '心脏病史', '2025-05-01', '血压<140/90，心率<100')
ON DUPLICATE KEY UPDATE blood_pressure=VALUES(blood_pressure);

-- 插入智能手环数据
INSERT INTO smartwatch (watch_id, bound_user_id, last_sync_time, gps_location, heart_rate, sleep_score, step_count, battery) VALUES
('SW01', 'E01', '2025-05-05 09:00:00', '39.9042,116.4074', 75, 80, 3000, 90),
('SW02', 'E02', '2025-05-05 09:00:00', '39.9043,116.4075', 70, 85, 2800, 88),
('SW03', 'E03', '2025-05-05 09:00:00', '39.9044,116.4076', 82, 78, 2600, 85)
ON DUPLICATE KEY UPDATE heart_rate=VALUES(heart_rate);

-- 插入紧急呼叫记录数据
INSERT INTO emergencycall (call_id, elderly_id, call_time, gps_location, status, related_person_id) VALUES
('EC01', 'E01', '2025-05-01 12:30:00', '39.9042,116.4074', '已解决', 'F01'),
('EC02', 'E02', '2025-05-02 09:15:00', '39.9043,116.4075', '待处理', 'W_B02')
ON DUPLICATE KEY UPDATE status=VALUES(status);

-- 插入健康科普文章数据
INSERT INTO healtharticle (article_id, title, content, category, publish_time, favorited_by_users, image_url, read_count, difficulty_level) VALUES
('A001', '老年人如何预防跌倒', '跌倒是老年人常见的意外伤害，严重时可能导致骨折甚至危及生命。预防跌倒需要从多个方面入手：1.保持居住环境安全，地面保持干燥，铺设防滑垫，在浴室、楼梯等关键位置安装扶手；2.选择合适的鞋子，避免穿拖鞋或高跟鞋；3.保持适度运动，增强肌肉力量和平衡能力；4.定期检查视力，及时更换眼镜；5.谨慎使用药物，注意药物副作用对平衡的影响。', '养生', '2025-04-30', 'E01,E03', '/static/images/articles/fall_prevention.jpg', 156, 1),
('A002', '突发心梗的急救处理', '心肌梗死是心血管疾病的急重症，正确的急救措施能够挽救生命：1.立即拨打120急救电话，详细说明患者情况和地址；2.让患者保持安静，采取半卧位或舒适体位；3.保持室内通风，解开患者衣领，确保呼吸道通畅；4.如有硝酸甘油等急救药物，可在医生指导下含服；5.密切观察患者意识和呼吸，如出现心跳呼吸停止，立即进行心肺复苏；6.记录发病时间和症状变化，为医生诊治提供参考。', '急救', '2025-04-30', 'E02,E05', '/static/images/articles/heart_attack.jpg', 203, 2),
('A003', '高血压患者的饮食管理', '高血压患者的饮食管理是控制血压的重要手段：1.控制钠盐摄入，每日食盐量不超过6克，避免腌制食品；2.增加钾的摄入，多吃新鲜蔬菜水果，如香蕉、橙子、菠菜等；3.控制总热量，维持理想体重；4.限制饱和脂肪酸摄入，选择瘦肉、鱼类、豆类等优质蛋白；5.戒烟限酒，避免浓茶、咖啡等刺激性饮品；6.规律进餐，少食多餐，避免暴饮暴食。', '饮食', '2025-05-01', 'E01,E02', '/static/images/articles/diet_management.jpg', 189, 2),
('A004', '糖尿病老人的血糖监测', '血糖监测是糖尿病管理的核心环节：1.掌握正确的血糖测量方法，选择合适的血糖仪；2.了解监测时间点，包括空腹血糖、餐后2小时血糖等；3.记录血糖数值和相关情况，如进食、运动、用药等；4.识别低血糖症状，如心慌、出汗、饥饿感等，及时处理；5.定期检查糖化血红蛋白，评估长期血糖控制情况；6.与医生保持沟通，根据血糖变化调整治疗方案。', '疾病管理', '2025-05-01', 'E03,E04', '/static/images/articles/blood_sugar.jpg', 167, 2),
('A005', '老年人适宜的运动方式', '适度运动对老年人健康至关重要：1.选择低强度有氧运动，如散步、太极拳、游泳等；2.运动前进行充分热身，运动后做好放松；3.控制运动强度，以运动时能正常交谈为宜；4.循序渐进，从每次15-20分钟开始，逐渐增加到30-45分钟；5.注意运动安全，选择平坦的场地，穿合适的运动鞋；6.根据身体状况调整运动计划，有慢性病者需在医生指导下运动。', '运动', '2025-05-02', 'E01,E04', '/static/images/articles/exercise.jpg', 142, 1),
('A006', '老年人睡眠质量改善', '良好的睡眠对老年人健康极其重要：1.建立规律的作息时间，每天同一时间上床睡觉和起床；2.创造良好的睡眠环境，保持卧室安静、黑暗、凉爽；3.睡前避免刺激性活动，如剧烈运动、看刺激性电视节目；4.控制午睡时间，不超过30分钟；5.避免睡前大量饮水和进食；6.如有睡眠障碍，及时就医，不要自行服用安眠药。', '养生', '2025-05-02', 'E02,E05', '/static/images/articles/sleep.jpg', 178, 1),
('A007', '老年人用药安全指南', '老年人用药需要特别注意安全：1.严格按医嘱用药，不可随意增减剂量或停药；2.了解药物的作用和副作用，出现不良反应及时就医；3.避免同时服用多种药物，防止药物相互作用；4.定期整理药箱，及时清理过期药物；5.服药时间要准确，可使用药盒或设置提醒；6.保存好药品说明书和处方，便于医生了解用药情况。', '用药', '2025-05-03', 'E01,E03,E05', '/static/images/articles/medication.jpg', 134, 2),
('A008', '老年人心理健康维护', '心理健康对老年人同样重要：1.保持积极乐观的心态，培养兴趣爱好；2.维持社交活动，与家人朋友保持联系；3.适度参与社区活动，避免孤独感；4.学会情绪管理，遇到困难时寻求帮助；5.保持学习的习惯，如读书、学习新技能等；6.如出现持续的情绪低落、焦虑等症状，及时寻求专业帮助。', '心理', '2025-05-03', 'E02,E04', '/static/images/articles/mental_health.jpg', 198, 1),
('A009', '老年人眼部保健常识', '眼部健康对老年人生活质量影响很大：1.定期进行眼科检查，及时发现和治疗眼部疾病；2.注意用眼卫生，避免长时间近距离用眼；3.保证充足的光线，避免在昏暗环境中阅读；4.多吃富含维生素A、C、E的食物，如胡萝卜、蓝莓等；5.控制血糖、血压，预防糖尿病性视网膜病变；6.出门时佩戴太阳镜，保护眼睛免受紫外线伤害。', '保健', '2025-05-04', 'E01,E02,E04', '/static/images/articles/eye_care.jpg', 123, 1),
('A010', '老年人骨质疏松预防', '骨质疏松是老年人常见疾病：1.保证充足的钙质摄入，多吃奶制品、豆制品、绿叶蔬菜；2.适量补充维生素D，多晒太阳，促进钙质吸收；3.进行适度的负重运动，如散步、爬楼梯等；4.戒烟限酒，避免过量咖啡因摄入；5.定期进行骨密度检查，及时发现骨质疏松；6.预防跌倒，避免骨折的发生。', '疾病管理', '2025-05-04', 'E03,E05', '/static/images/articles/osteoporosis.jpg', 145, 2)
ON DUPLICATE KEY UPDATE read_count=VALUES(read_count);

-- 创建视图：显示老人及其负责的社区工作人员
CREATE OR REPLACE VIEW elderly_worker_view AS
SELECT
    e.user_id,
    e.name AS elderly_name,
    e.region,
    w.worker_id,
    w.name AS worker_name,
    w.phone AS worker_phone
FROM elderlyuser e
LEFT JOIN communityworker w ON e.region = w.region;

-- 创建存储过程：自动分配区域
DELIMITER //
CREATE PROCEDURE AssignRegionToElderly(IN elderly_id VARCHAR(10))
BEGIN
    DECLARE assigned_region VARCHAR(10);

    -- 随机分配区域A或B
    SET assigned_region = IF(RAND() > 0.5, 'A', 'B');

    -- 更新老人的区域
    UPDATE elderlyuser
    SET region = assigned_region
    WHERE user_id = elderly_id;

    -- 返回分配结果
    SELECT elderly_id, assigned_region,
           CASE
               WHEN assigned_region = 'A' THEN '刘强'
               WHEN assigned_region = 'B' THEN '王丽'
               ELSE '未知'
           END AS responsible_worker;
END //
DELIMITER ;

COMMIT;

-- 显示创建结果
SELECT '数据库创建完成！老人注册后将自动分配区域A或B' AS message;
SELECT '区域A由刘强负责，区域B由王丽负责' AS responsibility;
