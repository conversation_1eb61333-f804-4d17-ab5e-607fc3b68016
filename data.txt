实体关系表
实体	属性（部分示例）
老年人用户	用户ID、密码、姓名、年龄、电话号码、住址、紧急联系人姓名、紧急联系人电话、健康档案ID、手环设备ID、已绑定家属ID列表
家属用户	家属ID、密码、姓名、与老年人关系、电话号码、绑定老年人ID列表
社区工作人员	工号（区域+序号）、姓名、管辖区域（如A、B、C)、电话号码
后台管理员	用户ID、密码
健康档案	健康档案ID、血压记录、血糖记录、用药记录（药品名+剂量+用药时间）、病史备注、更新时间（关联老年人ID）、健康阈值（血压、心率）
智能手环	手环设备ID、绑定用户ID、最后同步时间、GPS定位数据、心率、睡眠评分、运动步数、电量
订餐订单	订单ID、老年人ID、餐品详情、送餐地址、支付状态、配送状态、配送员ID
健康科普文章	文章ID、标题、内容、分类（养生/急救）、发布时间、收藏用户列表
紧急呼叫记录	记录ID、老年人ID、呼叫时间、GPS位置、处理状态（待处理/已响应/已解决）、（关联家属/社区工作人员）

老年人用户（ElderlyUser）

点击图片可查看完整电子表格
家属用户（FamilyMemberUser）

点击图片可查看完整电子表格
社区工作人员（CommunityWorker）

点击图片可查看完整电子表格
后台管理员（AdminUser）

点击图片可查看完整电子表格
健康档案（HealthRecord）

点击图片可查看完整电子表格
智能手环（SmartBracelet）

点击图片可查看完整电子表格
订餐订单（MealOrder）

点击图片可查看完整电子表格
健康科普文章（HealthArticle）

点击图片可查看完整电子表格
紧急呼叫记录（EmergencyCallRecord）

点击图片可查看完整电子表格

SQL建表
-- 老年人用户表
CREATE TABLE ElderlyUser (
    user_id VARCHAR(10) PRIMARY KEY,
    password VARCHAR(50),
    name VARCHAR(50),
    age INT,
    phone_number VARCHAR(20),
    address VARCHAR(100),
    emergency_contact_name VARCHAR(50),
    emergency_contact_phone VARCHAR(20),
    health_record_id VARCHAR(10),
    smartwatch_id VARCHAR(10),
    family_user_ids VARCHAR(100)
);

-- 家属用户表
CREATE TABLE FamilyUser (
    family_id VARCHAR(10) PRIMARY KEY,
    password VARCHAR(50),
    name VARCHAR(50),
    relation_to_elderly VARCHAR(20),
    phone_number VARCHAR(20),
    elderly_user_ids VARCHAR(100)
);

-- 社区工作人员表
CREATE TABLE CommunityWorker (
    worker_id VARCHAR(10) PRIMARY KEY,
    name VARCHAR(50),
    area CHAR(1),
    phone_number VARCHAR(20)
);

-- 后台管理员表
CREATE TABLE AdminUser (
    user_id VARCHAR(10) PRIMARY KEY,
    password VARCHAR(50)
);

-- 健康档案表
CREATE TABLE HealthRecord (
    health_record_id VARCHAR(10) PRIMARY KEY,
    blood_pressure VARCHAR(20),
    blood_sugar VARCHAR(10),
    medication_record VARCHAR(1000),
    medical_history VARCHAR(1000),
    update_time DATE,
    health_thresholds VARCHAR(1000)
);

-- 智能手环表
CREATE TABLE Smartwatch (
    smartwatch_id VARCHAR(10) PRIMARY KEY,
    bound_user_id VARCHAR(10),
    last_sync_time DATETIME,
    gps_data VARCHAR(100),
    heart_rate INT,
    sleep_score INT,
    step_count INT,
    battery_level INT
);

-- 订餐订单表
CREATE TABLE MealOrder (
    order_id VARCHAR(10) PRIMARY KEY,
    elderly_user_id VARCHAR(10),
    meal_detail VARCHAR(1000),
    delivery_address VARCHAR(100),
    payment_status VARCHAR(10),
    delivery_status VARCHAR(10),
    delivery_person_id VARCHAR(10)
);

-- 健康科普文章表
CREATE TABLE HealthArticle (
    article_id VARCHAR(10) PRIMARY KEY,
    title VARCHAR(100),
    content VARCHAR(1000),
    category VARCHAR(20),
    publish_time DATE,
    collected_by_users VARCHAR(1000)
);

-- 紧急呼叫记录表
CREATE TABLE EmergencyCallRecord (
    record_id VARCHAR(10) PRIMARY KEY,
    elderly_user_id VARCHAR(10),
    call_time DATETIME,
    gps_location VARCHAR(100),
    status VARCHAR(20),
    handler_id VARCHAR(10)
);

·  所有 ID 做了分类前缀区分（E=老年人，F=家属，HR=健康档案，SW=手环，W=工作人员，MO=订餐，EC=紧急呼叫，A=文章，AD=后台）；
·   后台管理员ID 改为 001, 002。

-- 老年人用户
CREATE TABLE ElderlyUser (
    user_id VARCHAR(10) PRIMARY KEY,
    password VARCHAR(50),
    name VARCHAR(50),
    age INT,
    phone VARCHAR(20),
    address VARCHAR(100),
    emergency_contact_name VARCHAR(50),
    emergency_contact_phone VARCHAR(20),
    health_record_id VARCHAR(10),
    smartwatch_id VARCHAR(10),
    bound_family_ids VARCHAR(100)
);

-- 家属用户
CREATE TABLE FamilyUser (
    family_id VARCHAR(10) PRIMARY KEY,
    password VARCHAR(50),
    name VARCHAR(50),
    relationship VARCHAR(20),
    phone VARCHAR(20),
    bound_elderly_id VARCHAR(10)
);

-- 社区工作人员
CREATE TABLE CommunityWorker (
    worker_id VARCHAR(10) PRIMARY KEY,
    name VARCHAR(50),
    region VARCHAR(10),
    phone VARCHAR(20)
);

-- 后台管理员
CREATE TABLE AdminUser (
    admin_id VARCHAR(10) PRIMARY KEY,
    password VARCHAR(50)
);

-- 健康档案
CREATE TABLE HealthRecord (
    record_id VARCHAR(10) PRIMARY KEY,
    blood_pressure VARCHAR(20),
    blood_sugar VARCHAR(20),
    medication_record VARCHAR(1000),
    medical_history VARCHAR(1000),
    update_time DATE,
    health_threshold VARCHAR(1000)
);

-- 智能手环
CREATE TABLE Smartwatch (
    watch_id VARCHAR(10) PRIMARY KEY,
    bound_user_id VARCHAR(10),
    last_sync_time DATETIME,
    gps_location VARCHAR(50),
    heart_rate INT,
    sleep_score INT,
    step_count INT,
    battery INT
);

-- 订餐订单
CREATE TABLE MealOrder (
    order_id VARCHAR(10) PRIMARY KEY,
    elderly_id VARCHAR(10),
    meal_detail VARCHAR(1000),
    delivery_address VARCHAR(200),
    payment_status VARCHAR(20),
    delivery_status VARCHAR(20),
    delivery_worker_id VARCHAR(10)
);

-- 健康科普文章
CREATE TABLE HealthArticle (
    article_id VARCHAR(10) PRIMARY KEY,
    title VARCHAR(200),
    content VARCHAR(1000),
    category VARCHAR(20),
    publish_time DATE,
    favorited_by_users VARCHAR(200)
);

-- 紧急呼叫记录
CREATE TABLE EmergencyCall (
    call_id VARCHAR(10) PRIMARY KEY,
    elderly_id VARCHAR(10),
    call_time DATETIME,
    gps_location VARCHAR(50),
    status VARCHAR(20),
    related_person_id VARCHAR(10)
);

-- 老年人用户
INSERT INTO ElderlyUser VALUES
('E01', '123456', '张建国', 78, '13800000001', '北京市朝阳区A1', '张志强', '13800000011', 'HR01', 'SW01', 'F01,F02'),
('E02', '123456', '李淑兰', 75, '13800000002', '北京市海淀区B2', '李朝阳', '13800000021', 'HR02', 'SW02', 'F03,F04'),
('E03', '123456', '王福海', 80, '13800000003', '北京市丰台区C3', '王宇轩', '13800000031', 'HR03', 'SW03', 'F05,F06'),
('E04', '123456', '陈桂英', 76, '13800000004', '北京市西城区D4', '陈俊豪', '13800000041', 'HR04', 'SW04', 'F07,F08'),
('E05', '123456', '赵德发', 82, '13800000005', '北京市东城区E5', '赵鹏飞', '13800000051', 'HR05', 'SW05', 'F09,F10');

-- 家属用户
INSERT INTO FamilyUser VALUES
('F01', '123456', '张志强', '儿子', '13800000011', 'E01'),
('F02', '123456', '张慧敏', '女儿', '13800000012', 'E01'),
('F03', '123456', '李朝阳', '儿子', '13800000021', 'E02'),
('F04', '123456', '李雅婷', '女儿', '13800000022', 'E02'),
('F05', '123456', '王宇轩', '儿子', '13800000031', 'E03'),
('F06', '123456', '王诗涵', '女儿', '13800000032', 'E03'),
('F07', '123456', '陈俊豪', '儿子', '13800000041', 'E04'),
('F08', '123456', '陈梦琪', '女儿', '13800000042', 'E04'),
('F09', '123456', '赵鹏飞', '儿子', '13800000051', 'E05'),
('F10', '123456', '赵婉晴', '女儿', '13800000052', 'E05');

-- 社区工作人员
INSERT INTO CommunityWorker VALUES
('W_A01', '刘强', 'A', '13900000001'),
('W_B02', '王丽', 'B', '13900000002'),
('W_C03', '孙涛', 'C', '13900000003');

-- 后台管理员
INSERT INTO AdminUser VALUES
('001', 'admin123'),
('002', 'admin456');

-- 健康档案
INSERT INTO HealthRecord VALUES
('HR01', '120/80', '5.6', '阿司匹林:100mg:2025-05-01', '高血压', '2025-05-01', '血压<140/90，心率<100'),
('HR02', '130/85', '6.2', '格列美脲:2mg:2025-05-01', '糖尿病', '2025-05-01', '血压<140/90，心率<100'),
('HR03', '140/90', '5.9', '贝那普利:10mg:2025-05-01', '心脏病史', '2025-05-01', '血压<140/90，心率<100'),
('HR04', '125/75', '5.3', '无', '轻微脑梗史', '2025-05-01', '血压<140/90，心率<100'),
('HR05', '135/88', '6.0', '二甲双胍:500mg:2025-05-01', '糖尿病史', '2025-05-01', '血压<140/90，心率<100');

-- 智能手环
INSERT INTO Smartwatch VALUES
('SW01', 'E01', '2025-05-05 09:00:00', '39.9042,116.4074', 75, 80, 3000, 90),
('SW02', 'E02', '2025-05-05 09:00:00', '39.9042,116.4075', 70, 85, 2800, 88),
('SW03', 'E03', '2025-05-05 09:00:00', '39.9042,116.4076', 82, 78, 2600, 85),
('SW04', 'E04', '2025-05-05 09:00:00', '39.9042,116.4077', 68, 90, 3100, 92),
('SW05', 'E05', '2025-05-05 09:00:00', '39.9042,116.4078', 76, 88, 2900, 87);

-- 订餐订单
INSERT INTO MealOrder VALUES
('MO01', 'E01', '午餐：红烧鸡腿套餐', '北京市朝阳区A1', '已支付', '已配送', 'W_A01'),
('MO02', 'E02', '晚餐：清蒸鲈鱼套餐', '北京市海淀区B2', '未支付', '待配送', 'W_B02');

-- 健康科普文章
INSERT INTO HealthArticle VALUES
('A001', '老年人如何预防跌倒', '保持地面干燥、防滑垫、扶手等。', '养生', '2025-04-30', 'E01,E03'),
('A002', '突发心梗的急救处理', '立即拨打120、保持通风、含服硝酸甘油。', '急救', '2025-04-30', 'E02,E05');

-- 紧急呼叫记录
INSERT INTO EmergencyCall VALUES
('EC01', 'E01', '2025-05-01 12:30:00', '39.9042,116.4074', '已解决', 'F01'),
('EC02', 'E02', '2025-05-02 09:15:00', '39.9042,116.4075', '待处理', 'W_B02');
